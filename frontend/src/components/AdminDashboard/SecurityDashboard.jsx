/**
 * 🔒 ENTERPRISE SECURITY DASHBOARD COMPONENT
 * 
 * Advanced security monitoring, threat detection, and compliance reporting
 * Enterprise-grade security management for Fulmark HVAC CRM
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Row, 
  Col, 
  Statistic, 
  Badge, 
  Alert, 
  Spin, 
  Typography, 
  Tabs,
  Button,
  Space,
  Tag,
  Timeline,
  Progress,
  Descriptions,
  List,
  Avatar
} from 'antd';
import { 
  SecurityScanOutlined, 
  ShieldOutlined, 
  WarningOutlined, 
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
  AuditOutlined,
  BugOutlined,
  SafetyOutlined,
  LockOutlined,
  UnlockOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { Gauge, Pie } from '@ant-design/plots';
import { request } from '@/request';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const SecurityDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [securityData, setSecurityData] = useState(null);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchSecurityData();
    // Auto-refresh every 30 seconds for security monitoring
    const interval = setInterval(fetchSecurityData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchSecurityData = async () => {
    try {
      setLoading(true);
      const [auditLog, threatDetection, complianceReport] = await Promise.all([
        request.get('/admin/security/audit-log'),
        request.get('/admin/security/threat-detection'),
        request.get('/admin/security/compliance-report')
      ]);

      setSecurityData({
        audit: auditLog.result,
        threats: threatDetection.result,
        compliance: complianceReport.result
      });
      setError(null);
    } catch (err) {
      console.error('Failed to fetch security data:', err);
      setError('Nie udało się pobrać danych bezpieczeństwa');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchSecurityData();
    setRefreshing(false);
  };

  const getThreatLevelColor = (level) => {
    const colors = {
      CRITICAL: '#f5222d',
      HIGH: '#fa8c16',
      MEDIUM: '#faad14',
      LOW: '#52c41a'
    };
    return colors[level] || '#d9d9d9';
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical':
        return <CloseCircleOutlined style={{ color: '#f5222d' }} />;
      case 'high':
        return <ExclamationCircleOutlined style={{ color: '#fa8c16' }} />;
      case 'medium':
        return <WarningOutlined style={{ color: '#faad14' }} />;
      case 'warning':
        return <WarningOutlined style={{ color: '#faad14' }} />;
      case 'info':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      default:
        return <EyeOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  const getComplianceColor = (score) => {
    if (score >= 90) return '#52c41a';
    if (score >= 80) return '#faad14';
    if (score >= 70) return '#fa8c16';
    return '#f5222d';
  };

  // Security Events Table Columns
  const securityEventsColumns = [
    {
      title: 'Zdarzenie',
      key: 'event',
      render: (_, record) => (
        <Space>
          {getSeverityIcon(record.severity)}
          <div>
            <Text strong>{record.type}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.description}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: 'Użytkownik',
      key: 'user',
      render: (_, record) => (
        record.userName ? (
          <div>
            <Text>{record.userName}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.userEmail}
            </Text>
          </div>
        ) : (
          <Text type="secondary">System</Text>
        )
      ),
    },
    {
      title: 'Kategoria',
      dataIndex: 'category',
      key: 'category',
      render: (category) => {
        const categoryColors = {
          authentication: 'blue',
          threat: 'red',
          user_management: 'green',
          data_access: 'orange',
          system: 'purple',
          api_security: 'cyan'
        };
        return <Tag color={categoryColors[category]}>{category}</Tag>;
      },
    },
    {
      title: 'Czas',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp) => (
        <div>
          <Text>{new Date(timestamp).toLocaleDateString('pl-PL')}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {new Date(timestamp).toLocaleTimeString('pl-PL')}
          </Text>
        </div>
      ),
    },
    {
      title: 'Poziom',
      dataIndex: 'severity',
      key: 'severity',
      render: (severity) => {
        const severityColors = {
          critical: 'red',
          high: 'orange',
          medium: 'gold',
          warning: 'yellow',
          info: 'green'
        };
        return <Tag color={severityColors[severity]}>{severity.toUpperCase()}</Tag>;
      },
    },
  ];

  // Prepare compliance chart data
  const prepareComplianceData = () => {
    if (!securityData?.compliance?.complianceReport) return [];
    
    const { complianceReport } = securityData.compliance;
    return [
      { type: 'GDPR', value: complianceReport.gdprCompliance.score },
      { type: 'ISO 27001', value: complianceReport.iso27001.score },
      { type: 'Data Protection', value: complianceReport.dataProtection.score }
    ];
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>Ładowanie danych bezpieczeństwa...</Text>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Błąd bezpieczeństwa"
        description={error}
        type="error"
        showIcon
        action={
          <Button onClick={fetchSecurityData}>
            Spróbuj ponownie
          </Button>
        }
      />
    );
  }

  const { audit, threats, compliance } = securityData;

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <Title level={2}>
          🔒 Dashboard Bezpieczeństwa Enterprise
        </Title>
        <Button 
          icon={<ReloadOutlined />} 
          onClick={handleRefresh}
          loading={refreshing}
        >
          Odśwież
        </Button>
      </div>

      {/* Security Overview */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Poziom Zagrożenia"
              value={threats?.threatDetection?.activeThreatLevel || 'LOW'}
              valueStyle={{ color: getThreatLevelColor(threats?.threatDetection?.activeThreatLevel) }}
              prefix={<ShieldOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Aktywne Zagrożenia"
              value={threats?.threatDetection?.detectedThreats?.length || 0}
              prefix={<BugOutlined />}
              valueStyle={{ color: threats?.threatDetection?.detectedThreats?.length > 0 ? '#f5222d' : '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Wynik Bezpieczeństwa"
              value={threats?.securityMetrics?.securityScore || 0}
              suffix="%"
              prefix={<SecurityScanOutlined />}
              valueStyle={{ color: getComplianceColor(threats?.securityMetrics?.securityScore || 0) }}
            />
            <Progress 
              percent={threats?.securityMetrics?.securityScore || 0} 
              strokeColor={getComplianceColor(threats?.securityMetrics?.securityScore || 0)}
              showInfo={false}
              size="small"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Zgodność"
              value={compliance?.overallScore || 0}
              suffix="%"
              prefix={<SafetyOutlined />}
              valueStyle={{ color: getComplianceColor(compliance?.overallScore || 0) }}
            />
            <Progress 
              percent={compliance?.overallScore || 0} 
              strokeColor={getComplianceColor(compliance?.overallScore || 0)}
              showInfo={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>

      {/* Security Alerts */}
      {threats?.threatDetection?.detectedThreats?.length > 0 && (
        <Alert
          message="Wykryto Zagrożenia Bezpieczeństwa!"
          description={
            <div>
              {threats.threatDetection.detectedThreats.map((threat, index) => (
                <div key={index} style={{ marginBottom: '8px' }}>
                  <Tag color="red">{threat.type}</Tag>
                  <Text>{threat.description}</Text>
                </div>
              ))}
            </div>
          }
          type="error"
          showIcon
          style={{ marginBottom: '24px' }}
        />
      )}

      <Tabs defaultActiveKey="events">
        <TabPane tab="Zdarzenia Bezpieczeństwa" key="events">
          <Card>
            <Table
              columns={securityEventsColumns}
              dataSource={audit?.securityEvents || []}
              pagination={{ pageSize: 20 }}
              size="small"
              scroll={{ x: 800 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="Wykrywanie Zagrożeń" key="threats">
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="Aktywne Zagrożenia" size="small">
                {threats?.threatDetection?.detectedThreats?.length > 0 ? (
                  <List
                    dataSource={threats.threatDetection.detectedThreats}
                    renderItem={(threat) => (
                      <List.Item>
                        <List.Item.Meta
                          avatar={<Avatar icon={<BugOutlined />} style={{ backgroundColor: '#f5222d' }} />}
                          title={
                            <Space>
                              <Text strong>{threat.type}</Text>
                              <Tag color="red">{threat.severity}</Tag>
                            </Space>
                          }
                          description={threat.description}
                        />
                      </List.Item>
                    )}
                  />
                ) : (
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <CheckCircleOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
                    <div style={{ marginTop: '16px' }}>
                      <Text>Nie wykryto aktywnych zagrożeń</Text>
                    </div>
                  </div>
                )}
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="Podejrzane Aktywności" size="small">
                {threats?.threatDetection?.suspiciousActivities?.length > 0 ? (
                  <List
                    dataSource={threats.threatDetection.suspiciousActivities}
                    renderItem={(activity) => (
                      <List.Item>
                        <List.Item.Meta
                          avatar={<Avatar icon={<WarningOutlined />} style={{ backgroundColor: '#faad14' }} />}
                          title={
                            <Space>
                              <Text strong>{activity.type}</Text>
                              <Tag color="orange">{activity.severity}</Tag>
                            </Space>
                          }
                          description={activity.description}
                        />
                      </List.Item>
                    )}
                  />
                ) : (
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <CheckCircleOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
                    <div style={{ marginTop: '16px' }}>
                      <Text>Brak podejrzanych aktywności</Text>
                    </div>
                  </div>
                )}
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="Zgodność" key="compliance">
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={8}>
              <Card title="Wynik Zgodności" size="small">
                <Gauge
                  percent={compliance?.overallScore / 100 || 0}
                  range={{
                    color: getComplianceColor(compliance?.overallScore || 0),
                  }}
                  indicator={{
                    pointer: {
                      style: {
                        stroke: '#D0D0D0',
                      },
                    },
                    pin: {
                      style: {
                        stroke: '#D0D0D0',
                      },
                    },
                  }}
                  statistic={{
                    content: {
                      style: {
                        fontSize: '36px',
                        lineHeight: '36px',
                      },
                      formatter: () => `${compliance?.overallScore || 0}%`,
                    },
                  }}
                />
              </Card>
            </Col>
            <Col xs={24} lg={16}>
              <Card title="Szczegóły Zgodności" size="small">
                <Row gutter={[16, 16]}>
                  {compliance?.complianceReport && Object.entries(compliance.complianceReport).map(([key, standard]) => (
                    <Col xs={24} md={8} key={key}>
                      <Card size="small" style={{ textAlign: 'center' }}>
                        <Statistic
                          title={key.toUpperCase()}
                          value={standard.score}
                          suffix="%"
                          valueStyle={{ color: getComplianceColor(standard.score) }}
                        />
                        <Progress 
                          percent={standard.score} 
                          strokeColor={getComplianceColor(standard.score)}
                          showInfo={false}
                          size="small"
                        />
                        <Tag color={standard.status === 'COMPLIANT' ? 'green' : 'orange'}>
                          {standard.status}
                        </Tag>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="Rekomendacje" key="recommendations">
          <Row gutter={[16, 16]}>
            <Col xs={24}>
              <Card title="Rekomendacje Bezpieczeństwa" size="small">
                {threats?.threatDetection?.securityRecommendations?.length > 0 ? (
                  <List
                    dataSource={threats.threatDetection.securityRecommendations}
                    renderItem={(rec) => (
                      <List.Item>
                        <List.Item.Meta
                          avatar={
                            <Avatar 
                              icon={<LockOutlined />} 
                              style={{ 
                                backgroundColor: rec.priority === 'HIGH' ? '#f5222d' : 
                                                rec.priority === 'MEDIUM' ? '#faad14' : '#52c41a' 
                              }} 
                            />
                          }
                          title={
                            <Space>
                              <Text strong>{rec.title}</Text>
                              <Tag color={rec.priority === 'HIGH' ? 'red' : rec.priority === 'MEDIUM' ? 'orange' : 'green'}>
                                {rec.priority}
                              </Tag>
                            </Space>
                          }
                          description={rec.description}
                        />
                      </List.Item>
                    )}
                  />
                ) : (
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <CheckCircleOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
                    <div style={{ marginTop: '16px' }}>
                      <Text>Brak rekomendacji - system jest bezpieczny!</Text>
                    </div>
                  </div>
                )}
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default SecurityDashboard;
