/**
 * 👥 ENTERPRISE USER MANAGEMENT COMPONENT
 * 
 * Advanced user management with RBAC, activity tracking, and session monitoring
 * Enterprise-grade user administration for Fulmark HVAC CRM
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Row, 
  Col, 
  Statistic, 
  Badge, 
  Alert, 
  Spin, 
  Typography, 
  Tabs,
  Button,
  Space,
  Tag,
  Timeline,
  Tooltip,
  Progress,
  Avatar,
  Descriptions
} from 'antd';
import { 
  UserOutlined, 
  TeamOutlined, 
  SecurityScanOutlined, 
  ClockCircleOutlined,
  LoginOutlined,
  LogoutOutlined,
  SettingOutlined,
  ShieldOutlined,
  ReloadOutlined,
  EyeOutlined,
  UserAddOutlined,
  UserDeleteOutlined
} from '@ant-design/icons';
import { request } from '@/request';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const UserManagement = () => {
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState(null);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('roles');

  useEffect(() => {
    fetchUserData();
    // Auto-refresh every 60 seconds
    const interval = setInterval(fetchUserData, 60000);
    return () => clearInterval(interval);
  }, []);

  const fetchUserData = async () => {
    try {
      setLoading(true);
      const [rolesMatrix, activityLog, sessions] = await Promise.all([
        request.get('/admin/users/roles-matrix'),
        request.get('/admin/users/activity-log'),
        request.get('/admin/users/sessions')
      ]);

      setUserData({
        roles: rolesMatrix.result,
        activities: activityLog.result,
        sessions: sessions.result
      });
      setError(null);
    } catch (err) {
      console.error('Failed to fetch user data:', err);
      setError('Nie udało się pobrać danych użytkowników');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchUserData();
    setRefreshing(false);
  };

  const getRoleColor = (role) => {
    const colors = {
      owner: 'red',
      admin: 'orange',
      manager: 'blue',
      employee: 'green',
      technician: 'cyan',
      read_only: 'default'
    };
    return colors[role] || 'default';
  };

  const getPermissionLevel = (level) => {
    const levels = {
      5: { color: '#f5222d', text: 'Właściciel' },
      4: { color: '#fa8c16', text: 'Administrator' },
      3: { color: '#1890ff', text: 'Menedżer' },
      2: { color: '#52c41a', text: 'Pracownik' },
      1: { color: '#d9d9d9', text: 'Tylko odczyt' }
    };
    return levels[level] || { color: '#d9d9d9', text: 'Nieznany' };
  };

  // Roles Matrix Table Columns
  const rolesColumns = [
    {
      title: 'Rola',
      dataIndex: 'role',
      key: 'role',
      render: (role, record) => (
        <Space>
          <Tag color={getRoleColor(role)}>{role}</Tag>
          <Text strong>{record.description}</Text>
        </Space>
      ),
    },
    {
      title: 'Poziom',
      dataIndex: 'level',
      key: 'level',
      render: (level) => {
        const levelInfo = getPermissionLevel(level);
        return (
          <Space>
            <Progress 
              percent={(level / 5) * 100} 
              strokeColor={levelInfo.color}
              showInfo={false}
              size="small"
              style={{ width: 60 }}
            />
            <Text style={{ color: levelInfo.color }}>{levelInfo.text}</Text>
          </Space>
        );
      },
    },
    {
      title: 'Użytkownicy',
      dataIndex: 'userCount',
      key: 'userCount',
      render: (count, record) => (
        <Space>
          <Badge count={count} showZero />
          <Text type={record.isActive ? 'success' : 'secondary'}>
            {record.isActive ? 'Aktywna' : 'Nieaktywna'}
          </Text>
        </Space>
      ),
    },
    {
      title: 'Uprawnienia',
      dataIndex: 'permissions',
      key: 'permissions',
      render: (permissions) => (
        <Tooltip title={permissions.join(', ')}>
          <Tag>{permissions.length} uprawnień</Tag>
        </Tooltip>
      ),
    },
  ];

  // Activity Log Table Columns
  const activityColumns = [
    {
      title: 'Użytkownik',
      key: 'user',
      render: (_, record) => (
        <Space>
          <Avatar icon={<UserOutlined />} size="small" />
          <div>
            <Text strong>{record.userName}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.userEmail}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: 'Akcja',
      dataIndex: 'action',
      key: 'action',
      render: (action, record) => {
        const actionIcons = {
          user_created: <UserAddOutlined style={{ color: '#52c41a' }} />,
          user_updated: <SettingOutlined style={{ color: '#1890ff' }} />,
          user_login: <LoginOutlined style={{ color: '#722ed1' }} />,
          user_deleted: <UserDeleteOutlined style={{ color: '#f5222d' }} />
        };
        
        return (
          <Space>
            {actionIcons[action] || <EyeOutlined />}
            <Text>{record.description}</Text>
          </Space>
        );
      },
    },
    {
      title: 'Czas',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp) => (
        <Tooltip title={new Date(timestamp).toLocaleString('pl-PL')}>
          <Text type="secondary">
            {new Date(timestamp).toLocaleDateString('pl-PL')} {new Date(timestamp).toLocaleTimeString('pl-PL')}
          </Text>
        </Tooltip>
      ),
    },
    {
      title: 'Kategoria',
      dataIndex: 'category',
      key: 'category',
      render: (category) => {
        const categoryColors = {
          user_management: 'blue',
          authentication: 'green',
          security: 'red',
          system: 'orange'
        };
        return <Tag color={categoryColors[category]}>{category}</Tag>;
      },
    },
  ];

  // Sessions Table Columns
  const sessionsColumns = [
    {
      title: 'Użytkownik',
      key: 'user',
      render: (_, record) => (
        <Space>
          <Avatar icon={<UserOutlined />} size="small" />
          <div>
            <Text strong>{record.userName}</Text>
            <br />
            <Tag color={getRoleColor(record.userRole)} size="small">
              {record.userRole}
            </Tag>
          </div>
        </Space>
      ),
    },
    {
      title: 'Status',
      key: 'status',
      render: (_, record) => (
        <Badge 
          status={record.userEnabled ? 'success' : 'error'} 
          text={record.isActive ? 'Aktywna sesja' : 'Nieaktywna'} 
        />
      ),
    },
    {
      title: 'Logowanie',
      dataIndex: 'loginDate',
      key: 'loginDate',
      render: (loginDate) => (
        <div>
          <Text>{new Date(loginDate).toLocaleDateString('pl-PL')}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {new Date(loginDate).toLocaleTimeString('pl-PL')}
          </Text>
        </div>
      ),
    },
    {
      title: 'Czas trwania',
      dataIndex: 'duration',
      key: 'duration',
      render: (duration) => {
        const hours = Math.floor(duration / (1000 * 60 * 60));
        const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
        return <Text>{hours}h {minutes}m</Text>;
      },
    },
    {
      title: 'IP Address',
      dataIndex: 'ipAddress',
      key: 'ipAddress',
      render: (ip) => <Text code>{ip}</Text>,
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>Ładowanie danych użytkowników...</Text>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Błąd zarządzania użytkownikami"
        description={error}
        type="error"
        showIcon
        action={
          <Button onClick={fetchUserData}>
            Spróbuj ponownie
          </Button>
        }
      />
    );
  }

  const { roles, activities, sessions } = userData;

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <Title level={2}>
          👥 Zarządzanie Użytkownikami Enterprise
        </Title>
        <Button 
          icon={<ReloadOutlined />} 
          onClick={handleRefresh}
          loading={refreshing}
        >
          Odśwież
        </Button>
      </div>

      {/* User Statistics Overview */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Łączna Liczba Użytkowników"
              value={roles?.statistics?.totalUsers || 0}
              prefix={<TeamOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Aktywne Role"
              value={roles?.statistics?.activeRoles || 0}
              suffix={`/ ${roles?.statistics?.totalRoles || 0}`}
              prefix={<ShieldOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Aktywne Sesje"
              value={sessions?.statistics?.totalActiveSessions || 0}
              prefix={<LoginOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Administratorzy"
              value={roles?.statistics?.highestPrivilegeUsers || 0}
              prefix={<SecurityScanOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="Macierz Ról" key="roles">
          <Card>
            <Table
              columns={rolesColumns}
              dataSource={roles?.rolesMatrix || []}
              pagination={false}
              size="small"
            />
          </Card>
        </TabPane>

        <TabPane tab="Dziennik Aktywności" key="activities">
          <Card>
            <Table
              columns={activityColumns}
              dataSource={activities?.activityLog || []}
              pagination={{ pageSize: 20 }}
              size="small"
              scroll={{ x: 800 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="Aktywne Sesje" key="sessions">
          <Card>
            <Table
              columns={sessionsColumns}
              dataSource={sessions?.sessions || []}
              pagination={{ pageSize: 15 }}
              size="small"
              scroll={{ x: 800 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="Statystyki" key="statistics">
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="Rozkład Ról" size="small">
                <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                  {roles?.roleDistribution?.map((role, index) => (
                    <div key={index} style={{ marginBottom: '12px' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Tag color={getRoleColor(role._id)}>{role._id}</Tag>
                        <Text strong>{role.count} użytkowników</Text>
                      </div>
                      <Progress 
                        percent={(role.count / (roles?.statistics?.totalUsers || 1)) * 100} 
                        size="small"
                        showInfo={false}
                      />
                    </div>
                  ))}
                </div>
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="Ostatnia Aktywność" size="small">
                <Timeline size="small" style={{ maxHeight: '300px', overflowY: 'auto' }}>
                  {activities?.activityLog?.slice(0, 10).map((activity, index) => (
                    <Timeline.Item
                      key={index}
                      color={activity.severity === 'high' ? 'red' : 
                             activity.severity === 'medium' ? 'orange' : 'blue'}
                    >
                      <div>
                        <Text strong>{activity.userName}</Text>
                        <br />
                        <Text type="secondary">{activity.description}</Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {new Date(activity.timestamp).toLocaleString('pl-PL')}
                        </Text>
                      </div>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default UserManagement;
