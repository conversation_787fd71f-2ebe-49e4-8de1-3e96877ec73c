import React, { useState } from 'react';
import { Layout, Menu, Typography, Card, Row, Col, Statistic } from 'antd';
import {
  UserOutlined,
  DashboardOutlined,
  DatabaseOutlined,
  RobotOutlined,
  BarChartOutlined,
  SettingOutlined,
  TeamOutlined,
  SecurityScanOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';

import CrudModule from '@/modules/CrudModule/CrudModule';
import AdminForm from '@/forms/AdminForm';
import SystemOverview from '@/components/AdminDashboard/SystemOverview';
import DatabaseManagement from '@/components/AdminDashboard/DatabaseManagement';
import AIServicesConfig from '@/components/AdminDashboard/AIServicesConfig';
import UserManagement from '@/components/AdminDashboard/UserManagement';
import SecurityDashboard from '@/components/AdminDashboard/SecurityDashboard';
import PerformanceAnalytics from '@/components/AdminDashboard/PerformanceAnalytics';
import useLanguage from '@/locale/useLanguage';

const { Content, Sider } = Layout;
const { Title } = Typography;

export default function Admin() {
  const [selectedKey, setSelectedKey] = useState('dashboard');
  const translate = useLanguage();

  const entity = 'admin';
  const searchConfig = {
    displayLabels: ['name', 'email'],
    searchFields: 'name,email',
  };
  const deleteModalLabels = ['name', 'email'];

  const Labels = {
    PANEL_TITLE: translate('admin'),
    DATATABLE_TITLE: translate('admin_list'),
    ADD_NEW_ENTITY: translate('add_new_admin'),
    ENTITY_NAME: translate('admin'),
  };
  const configPage = {
    entity,
    ...Labels,
  };
  const config = {
    ...configPage,
    searchConfig,
    deleteModalLabels,
  };

  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: 'Przegląd Systemu',
    },
    {
      key: 'database',
      icon: <DatabaseOutlined />,
      label: 'Baza Danych',
    },
    {
      key: 'ai-services',
      icon: <RobotOutlined />,
      label: 'Usługi AI',
    },
    {
      key: 'user-management',
      icon: <TeamOutlined />,
      label: 'Zarządzanie Użytkownikami',
    },
    {
      key: 'security',
      icon: <SecurityScanOutlined />,
      label: 'Bezpieczeństwo',
    },
    {
      key: 'performance',
      icon: <ThunderboltOutlined />,
      label: 'Wydajność',
    },
    {
      key: 'users',
      icon: <TeamOutlined />,
      label: 'CRUD Użytkownicy',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Ustawienia',
    },
  ];

  const renderContent = () => {
    switch (selectedKey) {
      case 'dashboard':
        return <SystemOverview />;
      case 'database':
        return <DatabaseManagement />;
      case 'ai-services':
        return <AIServicesConfig />;
      case 'user-management':
        return <UserManagement />;
      case 'security':
        return <SecurityDashboard />;
      case 'performance':
        return <PerformanceAnalytics />;
      case 'users':
        return (
          <CrudModule
            createForm={<AdminForm isUpdateForm={false} />}
            updateForm={<AdminForm isUpdateForm={true} />}
            config={config}
          />
        );
      case 'settings':
        return (
          <div style={{ padding: '24px' }}>
            <Title level={2}>⚙️ Ustawienia Systemu Enterprise</Title>
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="Konfiguracje Systemu"
                    value={12}
                    prefix={<SettingOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="Aktywne Integracje"
                    value={8}
                    prefix={<DatabaseOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="Backup Jobs"
                    value={3}
                    prefix={<UserOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="Scheduled Tasks"
                    value={15}
                    prefix={<BarChartOutlined />}
                  />
                </Card>
              </Col>
            </Row>
            <Card style={{ marginTop: '24px' }}>
              <Title level={4}>🚀 Enterprise Features</Title>
              <p>Panel ustawień enterprise z zaawansowanymi opcjami konfiguracji będzie dostępny wkrótce...</p>
              <ul>
                <li>🔧 Konfiguracja systemu</li>
                <li>🔄 Zarządzanie backup</li>
                <li>📊 Harmonogram zadań</li>
                <li>🔗 Integracje zewnętrzne</li>
                <li>📧 Konfiguracja powiadomień</li>
                <li>🛡️ Ustawienia bezpieczeństwa</li>
              </ul>
            </Card>
          </div>
        );
      default:
        return <SystemOverview />;
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider width={250} theme="light" style={{ borderRight: '1px solid #f0f0f0' }}>
        <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>
          <Title level={4} style={{ margin: 0, textAlign: 'center' }}>
            🔧 Admin Panel
          </Title>
        </div>
        <Menu
          mode="inline"
          selectedKeys={[selectedKey]}
          items={menuItems}
          onClick={({ key }) => setSelectedKey(key)}
          style={{ border: 'none' }}
        />
      </Sider>
      <Layout>
        <Content style={{ background: '#f5f5f5' }}>
          {renderContent()}
        </Content>
      </Layout>
    </Layout>
  );
}
