const createUserController = require('@/controllers/middlewaresControllers/createUserController');

// Enhanced Admin Controller with system management features
const adminController = createUserController('Admin');

// System monitoring endpoints
const systemStatus = require('./systemStatus');
const databaseStats = require('./databaseStats');
const aiServicesStatus = require('./aiServicesStatus');
const userAnalytics = require('./userAnalytics');
const performanceMetrics = require('./performanceMetrics');

// Enterprise user management endpoints
const userManagement = require('./userManagement');
const securityAudit = require('./securityAudit');

// Add enhanced admin endpoints
adminController.systemStatus = systemStatus;
adminController.databaseStats = databaseStats;
adminController.aiServicesStatus = aiServicesStatus;
adminController.userAnalytics = userAnalytics;
adminController.performanceMetrics = performanceMetrics;

// Add enterprise user management endpoints
adminController.getUserRolesMatrix = userManagement.getUserRolesMatrix;
adminController.getUserActivityLog = userManagement.getUserActivityLog;
adminController.getUserSessions = userManagement.getUserSessions;

// Add security & audit endpoints
adminController.getSecurityAuditLog = securityAudit.getSecurityAuditLog;
adminController.getThreatDetection = securityAudit.getThreatDetection;
adminController.getComplianceReport = securityAudit.getComplianceReport;

module.exports = adminController;
