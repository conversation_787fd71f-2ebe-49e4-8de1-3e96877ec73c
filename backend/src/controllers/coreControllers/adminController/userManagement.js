/**
 * 👥 ENTERPRISE USER MANAGEMENT CONTROLLER
 * 
 * Advanced user management with RBAC, activity tracking, and security monitoring
 * Enterprise-grade user administration for Fulmark HVAC CRM
 */

const mongoose = require('mongoose');

// Role-based access control matrix
const ROLE_PERMISSIONS = {
  owner: {
    level: 5,
    permissions: ['*'], // All permissions
    description: 'Full system access - Owner level'
  },
  admin: {
    level: 4,
    permissions: [
      'users.create', 'users.read', 'users.update', 'users.delete',
      'system.config', 'system.monitor', 'system.backup',
      'data.export', 'data.import', 'reports.all'
    ],
    description: 'Administrative access with system management'
  },
  manager: {
    level: 3,
    permissions: [
      'users.read', 'users.update',
      'customers.all', 'opportunities.all', 'quotes.all',
      'reports.business', 'analytics.view'
    ],
    description: 'Management access with business operations'
  },
  employee: {
    level: 2,
    permissions: [
      'customers.read', 'customers.update',
      'opportunities.read', 'opportunities.update',
      'tasks.all', 'calendar.all'
    ],
    description: 'Standard employee access'
  },
  technician: {
    level: 2,
    permissions: [
      'serviceorders.all', 'equipment.all',
      'customers.read', 'tasks.all',
      'mobile.access'
    ],
    description: 'Field technician access'
  },
  read_only: {
    level: 1,
    permissions: [
      'customers.read', 'opportunities.read',
      'reports.view', 'dashboard.view'
    ],
    description: 'Read-only access'
  }
};

const getUserRolesMatrix = async (req, res) => {
  try {
    console.log('👥 Admin User Management - Fetching roles matrix...');

    const Admin = mongoose.model('Admin');
    
    // Get user distribution by role
    const roleDistribution = await Admin.aggregate([
      { $match: { removed: { $ne: true } } },
      { $group: { _id: '$role', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    // Get recent user activities
    const recentActivities = await Admin.find({
      removed: { $ne: true },
      updatedAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
    })
    .select('name email role updatedAt lastLogin')
    .sort({ updatedAt: -1 })
    .limit(20);

    // Build comprehensive roles matrix
    const rolesMatrix = Object.entries(ROLE_PERMISSIONS).map(([role, config]) => {
      const userCount = roleDistribution.find(r => r._id === role)?.count || 0;
      return {
        role,
        level: config.level,
        permissions: config.permissions,
        description: config.description,
        userCount,
        isActive: userCount > 0
      };
    });

    // Permission categories for better organization
    const permissionCategories = {
      users: ['users.create', 'users.read', 'users.update', 'users.delete'],
      system: ['system.config', 'system.monitor', 'system.backup'],
      customers: ['customers.create', 'customers.read', 'customers.update', 'customers.delete', 'customers.all'],
      opportunities: ['opportunities.create', 'opportunities.read', 'opportunities.update', 'opportunities.delete', 'opportunities.all'],
      serviceorders: ['serviceorders.create', 'serviceorders.read', 'serviceorders.update', 'serviceorders.delete', 'serviceorders.all'],
      equipment: ['equipment.create', 'equipment.read', 'equipment.update', 'equipment.delete', 'equipment.all'],
      reports: ['reports.view', 'reports.business', 'reports.all'],
      data: ['data.export', 'data.import'],
      analytics: ['analytics.view', 'analytics.advanced'],
      mobile: ['mobile.access'],
      tasks: ['tasks.all'],
      calendar: ['calendar.all'],
      quotes: ['quotes.all'],
      dashboard: ['dashboard.view']
    };

    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      rolesMatrix,
      permissionCategories,
      roleDistribution,
      recentActivities: recentActivities.map(user => ({
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        lastActivity: user.updatedAt,
        lastLogin: user.lastLogin || null
      })),
      statistics: {
        totalUsers: roleDistribution.reduce((sum, role) => sum + role.count, 0),
        totalRoles: Object.keys(ROLE_PERMISSIONS).length,
        activeRoles: rolesMatrix.filter(r => r.isActive).length,
        highestPrivilegeUsers: roleDistribution.filter(r => 
          ROLE_PERMISSIONS[r._id]?.level >= 4
        ).reduce((sum, role) => sum + role.count, 0)
      }
    };

    console.log('✅ User Roles Matrix - Successfully fetched roles and permissions');
    console.log(`👤 Total Users: ${response.statistics.totalUsers}, Active Roles: ${response.statistics.activeRoles}`);

    res.status(200).json(response);

  } catch (error) {
    console.error('❌ User Roles Matrix Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user roles matrix',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
};

const getUserActivityLog = async (req, res) => {
  try {
    console.log('📊 Admin User Management - Fetching user activity log...');

    const { userId, timeRange = '7d', limit = 50 } = req.query;
    
    const Admin = mongoose.model('Admin');
    const AdminPassword = mongoose.model('AdminPassword');

    // Calculate time range
    const timeRanges = {
      '1d': 1 * 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000,
      '90d': 90 * 24 * 60 * 60 * 1000
    };

    const timeFilter = new Date(Date.now() - (timeRanges[timeRange] || timeRanges['7d']));

    // Build query
    let query = { removed: { $ne: true } };
    if (userId) {
      query._id = userId;
    }

    // Get user activities
    const users = await Admin.find(query)
      .select('name email role createdAt updatedAt enabled')
      .sort({ updatedAt: -1 })
      .limit(parseInt(limit));

    // Get login sessions
    const loginSessions = await AdminPassword.find({
      user: { $in: users.map(u => u._id) },
      'loggedSessions.loginDate': { $gte: timeFilter }
    })
    .populate('user', 'name email role')
    .select('user loggedSessions');

    // Process activity data
    const activityLog = [];

    // Add user creation/update activities
    users.forEach(user => {
      if (user.createdAt >= timeFilter) {
        activityLog.push({
          id: `create_${user._id}`,
          userId: user._id,
          userName: user.name,
          userEmail: user.email,
          userRole: user.role,
          action: 'user_created',
          description: `User account created`,
          timestamp: user.createdAt,
          severity: 'info',
          category: 'user_management'
        });
      }

      if (user.updatedAt >= timeFilter && user.updatedAt > user.createdAt) {
        activityLog.push({
          id: `update_${user._id}`,
          userId: user._id,
          userName: user.name,
          userEmail: user.email,
          userRole: user.role,
          action: 'user_updated',
          description: `User account updated`,
          timestamp: user.updatedAt,
          severity: 'info',
          category: 'user_management'
        });
      }
    });

    // Add login activities
    loginSessions.forEach(session => {
      if (session.loggedSessions) {
        session.loggedSessions
          .filter(login => new Date(login.loginDate) >= timeFilter)
          .forEach(login => {
            activityLog.push({
              id: `login_${session.user._id}_${login.loginDate}`,
              userId: session.user._id,
              userName: session.user.name,
              userEmail: session.user.email,
              userRole: session.user.role,
              action: 'user_login',
              description: `User logged in`,
              timestamp: new Date(login.loginDate),
              severity: 'info',
              category: 'authentication',
              metadata: {
                sessionId: login._id,
                ipAddress: login.ipAddress || 'Unknown'
              }
            });
          });
      }
    });

    // Sort by timestamp (newest first)
    activityLog.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Activity statistics
    const statistics = {
      totalActivities: activityLog.length,
      uniqueUsers: new Set(activityLog.map(a => a.userId)).size,
      activityTypes: {
        user_created: activityLog.filter(a => a.action === 'user_created').length,
        user_updated: activityLog.filter(a => a.action === 'user_updated').length,
        user_login: activityLog.filter(a => a.action === 'user_login').length
      },
      timeRange,
      period: `${timeRange} (${new Date(timeFilter).toLocaleDateString('pl-PL')} - ${new Date().toLocaleDateString('pl-PL')})`
    };

    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      activityLog: activityLog.slice(0, parseInt(limit)),
      statistics,
      filters: {
        userId,
        timeRange,
        limit: parseInt(limit)
      }
    };

    console.log('✅ User Activity Log - Successfully fetched activity data');
    console.log(`📊 Activities: ${statistics.totalActivities}, Users: ${statistics.uniqueUsers}`);

    res.status(200).json(response);

  } catch (error) {
    console.error('❌ User Activity Log Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user activity log',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
};

const getUserSessions = async (req, res) => {
  try {
    console.log('🔐 Admin User Management - Fetching user sessions...');

    const AdminPassword = mongoose.model('AdminPassword');

    // Get all active sessions
    const activeSessions = await AdminPassword.find({
      loggedSessions: { $exists: true, $ne: [] }
    })
    .populate('user', 'name email role enabled')
    .select('user loggedSessions');

    // Process session data
    const sessionData = [];
    let totalSessions = 0;

    activeSessions.forEach(userSession => {
      if (userSession.loggedSessions && userSession.user) {
        userSession.loggedSessions.forEach(session => {
          totalSessions++;
          sessionData.push({
            sessionId: session._id,
            userId: userSession.user._id,
            userName: userSession.user.name,
            userEmail: userSession.user.email,
            userRole: userSession.user.role,
            userEnabled: userSession.user.enabled,
            loginDate: session.loginDate,
            ipAddress: session.ipAddress || 'Unknown',
            userAgent: session.userAgent || 'Unknown',
            isActive: true, // All sessions in loggedSessions are considered active
            duration: Date.now() - new Date(session.loginDate).getTime(),
            location: session.location || 'Unknown'
          });
        });
      }
    });

    // Sort by login date (newest first)
    sessionData.sort((a, b) => new Date(b.loginDate) - new Date(a.loginDate));

    // Session statistics
    const statistics = {
      totalActiveSessions: totalSessions,
      uniqueUsers: new Set(sessionData.map(s => s.userId)).size,
      roleDistribution: sessionData.reduce((acc, session) => {
        acc[session.userRole] = (acc[session.userRole] || 0) + 1;
        return acc;
      }, {}),
      averageSessionDuration: sessionData.length > 0 ? 
        sessionData.reduce((sum, s) => sum + s.duration, 0) / sessionData.length : 0,
      recentLogins: sessionData.filter(s => 
        Date.now() - new Date(s.loginDate).getTime() < 24 * 60 * 60 * 1000
      ).length
    };

    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      sessions: sessionData,
      statistics
    };

    console.log('✅ User Sessions - Successfully fetched session data');
    console.log(`🔐 Active Sessions: ${totalSessions}, Unique Users: ${statistics.uniqueUsers}`);

    res.status(200).json(response);

  } catch (error) {
    console.error('❌ User Sessions Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user sessions',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
};

module.exports = {
  getUserRolesMatrix,
  getUserActivityLog,
  getUserSessions
};
